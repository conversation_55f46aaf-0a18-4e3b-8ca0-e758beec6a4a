import { writable, derived, get } from 'svelte/store';
import type { CustomerPlatformIdentity } from '$lib/types/customer';
import { CustomerService } from '$lib/api/features/customer/customers.service';

// Tab state interface
interface TabState {
    platforms: CustomerPlatformIdentity[];
    currentPage: number;
    loadedItemsCount: number;
    totalCount: number;
    hasMore: boolean;
    loading: boolean;
    lastUpdated: Date;
}

// Main cache state interface
interface TabCacheState {
    tabs: Record<string, TabState>;
    activeTab: string;
    searchTerm: string;
    platformFilter: string;
    currentUser: string;
    accessToken: string;
    isPolling: boolean;
    pollingInterval: number | null;
}

// Initialize default tab state
function createDefaultTabState(): TabState {
    return {
        platforms: [],
        currentPage: 1,
        loadedItemsCount: 0,
        totalCount: 0,
        hasMore: false,
        loading: false,
        lastUpdated: new Date()
    };
}

// Create the store
function createTabCacheStore() {
    const { subscribe, set, update } = writable<TabCacheState>({
        tabs: {
            'my-assigned': createDefaultTabState(),
            'my-closed': createDefaultTabState(),
            'open': createDefaultTabState(),
            'others-assigned': createDefaultTabState()
        },
        activeTab: 'my-assigned',
        searchTerm: '',
        platformFilter: '',
        currentUser: '',
        accessToken: '',
        isPolling: false,
        pollingInterval: null
    });

    const customerService = new CustomerService();

    return {
        subscribe,
        
        // Initialize the store with server data
        initialize: (tabPaginationData: Record<string, any>, accessToken: string, username: string) => {
            // console.log('[DEBUG] tabCacheStore.initialize: Starting cache initialization', {
            //     username,
            //     tabDataKeys: Object.keys(tabPaginationData)
            // });

            update(state => {
                state.accessToken = accessToken;
                state.currentUser = username;

                // Initialize each tab with server data
                Object.keys(state.tabs).forEach(tabId => {
                    if (tabPaginationData[tabId]) {
                        const tabData = tabPaginationData[tabId];
                        const platforms = tabData.platforms || [];

                        // console.log(`[DEBUG] tabCacheStore.initialize: Initializing tab "${tabId}"`, {
                        //     platformCount: platforms.length,
                        //     platformIds: platforms.map((p: any) => p.id),
                        //     hasMore: tabData.hasMore,
                        //     totalCount: tabData.total || 0
                        // });

                        // Check for duplicate IDs during initialization
                        if (platforms.length > 0) {
                            const ids = platforms.map((p: any) => p.id);
                            const uniqueIds = [...new Set(ids)];
                            if (ids.length !== uniqueIds.length) {
                                console.error(`🚨 [ERROR] tabCacheStore.initialize: Duplicate platform IDs detected during initialization for tab "${tabId}":`, {
                                    allIds: ids,
                                    duplicates: ids.filter((id: any, index: number) => ids.indexOf(id) !== index)
                                });
                            }
                        }

                        state.tabs[tabId] = {
                            platforms: platforms,
                            currentPage: 1,
                            loadedItemsCount: platforms.length,
                            totalCount: tabData.total || 0,
                            hasMore: tabData.hasMore || false,
                            loading: false,
                            lastUpdated: new Date()
                        };
                    } else {
                        // console.log(`[DEBUG] tabCacheStore.initialize: No data provided for tab "${tabId}"`);
                    }
                });

                console.log('[DEBUG] tabCacheStore.initialize: Cache initialization complete', {
                    activeTab: state.activeTab,
                    tabStates: Object.keys(state.tabs).reduce((acc, tabId) => {
                        acc[tabId] = {
                            platformCount: state.tabs[tabId].platforms.length,
                            totalCount: state.tabs[tabId].totalCount,
                            hasMore: state.tabs[tabId].hasMore,
                            loading: state.tabs[tabId].loading
                        };
                        return acc;
                    }, {} as Record<string, any>)
                });

                return state;
            });
        },

        // Switch to a different tab (instant - no API calls)
        switchTab: (tabId: string) => {
            // console.log(`[DEBUG] tabCacheStore.switchTab: Switching to tab "${tabId}"`);

            update(state => {
                const previousTab = state.activeTab;
                state.activeTab = tabId;

                const currentTabState = state.tabs[tabId];
                console.log(`🔍 [DEBUG] tabCacheStore.switchTab: Tab "${tabId}" cache state:`, {
                    previousTab,
                    newTab: tabId,
                    platformCount: currentTabState.platforms.length,
                    platformIds: currentTabState.platforms.map(p => p.id),
                    totalCount: currentTabState.totalCount,
                    hasMore: currentTabState.hasMore,
                    loading: currentTabState.loading,
                    currentPage: currentTabState.currentPage,
                    loadedItemsCount: currentTabState.loadedItemsCount
                });

                return state;
            });
        },

        // Load more items for a specific tab
        loadMore: async (tabId: string) => {
            const state = get({ subscribe });
            const tabState = state.tabs[tabId];

            console.log(`[DEBUG] tabCacheStore.loadMore: Starting load more for tab "${tabId}"`, {
                currentPlatformCount: tabState.platforms.length,
                currentPage: tabState.currentPage,
                totalCount: tabState.totalCount,
                hasMore: tabState.hasMore,
                loading: tabState.loading,
                loadedItemsCount: tabState.loadedItemsCount
            });

            if (tabState.loading || !tabState.hasMore) {
                // console.log(`[DEBUG] tabCacheStore.loadMore: Skipping load more for tab "${tabId}"`, {
                //     reason: tabState.loading ? 'already loading' : 'no more items',
                //     loading: tabState.loading,
                //     hasMore: tabState.hasMore
                // });
                return;
            }

            // Log current platform IDs before loading more
            // console.log(`[DEBUG] tabCacheStore.loadMore: Current platform IDs before load more:`, {
            //     tab: tabId,
            //     platformIds: tabState.platforms.map(p => p.id)
            // });

            update(s => {
                s.tabs[tabId].loading = true;
                return s;
            });

            try {
                const nextPage = tabState.currentPage + 1;
                console.log(`[DEBUG] tabCacheStore.loadMore: Requesting page ${nextPage} for tab "${tabId}"`);

                const result = await customerService.getPlatformIdentitiesWithFilters(state.accessToken, {
                    tab: tabId,
                    currentUser: state.currentUser,
                    search: state.searchTerm || undefined,
                    platform: state.platformFilter || undefined,
                    page: nextPage,
                    pageSize: 2
                });

                console.log(`[DEBUG] tabCacheStore.loadMore: Load more API response for tab "${tabId}":`, {
                    status: result.res_status,
                    newItemsCount: result.results?.length || 0,
                    newPlatformIds: result.results?.map(p => p.id) || [],
                    hasNext: !!result.next,
                    totalCount: result.count || 0
                });

                if (result.res_status === 200) {
                    // Check for duplicate IDs before merging
                    const existingIds = tabState.platforms.map(p => p.id);
                    const newIds = result.results?.map(p => p.id) || [];
                    
                    // const duplicateIds = newIds.filter(id => existingIds.includes(id));
                    // if (duplicateIds.length > 0) {
                    //     console.error(`[ERROR] tabCacheStore.loadMore: Duplicate platform IDs detected when loading more for tab "${tabId}":`, {
                    //         existingIds,
                    //         newIds,
                    //         duplicateIds
                    //     });
                    // }

                    update(s => {
                        const beforeCount = s.tabs[tabId].platforms.length;
                        s.tabs[tabId].platforms = [...s.tabs[tabId].platforms, ...result.results];
                        s.tabs[tabId].currentPage += 1;
                        s.tabs[tabId].loadedItemsCount = s.tabs[tabId].platforms.length;
                        s.tabs[tabId].totalCount = result.count || 0;
                        s.tabs[tabId].hasMore = !!result.next && s.tabs[tabId].loadedItemsCount < s.tabs[tabId].totalCount;
                        s.tabs[tabId].loading = false;
                        s.tabs[tabId].lastUpdated = new Date();

                        console.log(`🔍 [DEBUG] tabCacheStore.loadMore: Cache updated for tab "${tabId}":`, {
                            beforeCount,
                            afterCount: s.tabs[tabId].platforms.length,
                            addedCount: s.tabs[tabId].platforms.length - beforeCount,
                            newCurrentPage: s.tabs[tabId].currentPage,
                            totalCount: s.tabs[tabId].totalCount,
                            hasMore: s.tabs[tabId].hasMore,
                            allPlatformIds: s.tabs[tabId].platforms.map(p => p.id)
                        });

                        return s;
                    });
                } else {
                    console.error(`[ERROR] tabCacheStore.loadMore: Failed to load more for tab "${tabId}":`, result.error_msg);
                    update(s => {
                        s.tabs[tabId].loading = false;
                        return s;
                    });
                }
            } catch (error) {
                console.error(`[ERROR] tabCacheStore.loadMore: Exception loading more for tab ${tabId}:`, error);
                update(s => {
                    s.tabs[tabId].loading = false;
                    return s;
                });
            }
        },

        // Update search term and refresh data
        updateSearch: (searchTerm: string) => {
            update(state => {
                state.searchTerm = searchTerm;
                return state;
            });
        },

        // Update platform filter and refresh data
        updatePlatformFilter: (platformFilter: string) => {
            update(state => {
                state.platformFilter = platformFilter;
                return state;
            });
        },

        // Start background polling for all tabs
        startPolling: () => {
            const state = get({ subscribe });
            if (state.isPolling) return;

            update(s => {
                s.isPolling = true;
                s.pollingInterval = setInterval(async () => {
                    await pollAllTabs();
                }, 3000) as any; // Poll every 3 seconds
                return s;
            });
        },

        // Stop background polling
        stopPolling: () => {
            update(state => {
                if (state.pollingInterval) {
                    clearInterval(state.pollingInterval);
                    state.pollingInterval = null;
                }
                state.isPolling = false;
                return state;
            });
        },

        // Manual refresh for all tabs
        refreshAllTabs: async () => {
            await pollAllTabs();
        }
    };

    // Internal function to poll all tabs
    async function pollAllTabs() {
        const state = get({ subscribe });
        
        try {
            // Poll each tab based on its current loaded items count
            const tabPromises = Object.keys(state.tabs).map(async (tabId) => {
                const tabState = state.tabs[tabId];
                const pagesNeeded = Math.ceil(tabState.loadedItemsCount / 2) || 1;

                const result = await customerService.getPlatformIdentitiesForPolling(state.accessToken, {
                    tab: tabId,
                    currentUser: state.currentUser,
                    search: state.searchTerm || undefined,
                    platform: state.platformFilter || undefined,
                    loadedPages: pagesNeeded,
                    pageSize: 2
                });

                return { tabId, result };
            });

            const results = await Promise.all(tabPromises);

            // Update all tabs with fresh data
            update(s => {
                results.forEach(({ tabId, result }) => {
                    if (result.res_status === 200) {
                        s.tabs[tabId].platforms = result.results;
                        s.tabs[tabId].totalCount = result.count || 0;
                        s.tabs[tabId].hasMore = !!result.next && result.results.length < s.tabs[tabId].totalCount;
                        s.tabs[tabId].lastUpdated = new Date();
                    }
                });
                return s;
            });

        } catch (error) {
            console.error('Error during background polling:', error);
        }
    }
}

export const tabCacheStore = createTabCacheStore();

// Derived stores for easy access
export const activeTabData = derived(
    tabCacheStore,
    $tabCacheStore => $tabCacheStore.tabs[$tabCacheStore.activeTab]
);

export const currentTabPlatforms = derived(
    activeTabData,
    $activeTabData => $activeTabData?.platforms || []
);

export const isCurrentTabLoading = derived(
    activeTabData,
    $activeTabData => $activeTabData?.loading || false
);

export const currentTabHasMore = derived(
    activeTabData,
    $activeTabData => $activeTabData?.hasMore || false
);

export const currentTabTotalCount = derived(
    activeTabData,
    $activeTabData => $activeTabData?.totalCount || 0
);
