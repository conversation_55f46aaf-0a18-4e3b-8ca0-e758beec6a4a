<!-- +page.svelte -->
<script lang="ts">
	import { onMount, onDestroy } from 'svelte';
	import PlatformIdentityList from '$lib/components/chat/PlatformIdentityList.svelte';
	import ConversationView from '$lib/components/conversation/ConversationView.svelte';
	import CustomerInfoPanel from '$lib/components/customer/CustomerInfoPanel.svelte';
	import { platformWebSocket } from '$lib/websocket/platformWebSocket';
	import type { CustomerPlatformIdentity, Customer } from '$lib/types/customer';
	import { t } from '$lib/stores/i18n';
	import { CustomerService } from '$lib/api/features/customer/customers.service';
	import { getSubscriptionFromStorage } from '$lib/stores/subscriptionStatus';
	import { goto } from '$app/navigation';

	// Import the new caching store
	import {
		tabCacheStore,
		activeTabData,
		currentTabPlatforms,
		isCurrentTabLoading,
		currentTabHasMore,
		currentTabTotalCount
	} from '$lib/stores/tabCacheStore';

	export let data;

	// Initialize customer service
	const customerService = new CustomerService();

	let selectedPlatformId: number | null = null;
	let selectedCustomerId: number | null = null;
	let selectedCustomer: Customer | null = null;
	let selectedPlatformIdentity: CustomerPlatformIdentity | null = null;
	let latestTicketId: number | null = null;
	let loading = false;

	// Current active tab tracking
	let currentActiveTab = 'my-assigned';

	// Subscribe to the cache store for reactive updates
	$: currentPlatforms = $currentTabPlatforms;
	$: isLoading = $isCurrentTabLoading;
	$: hasMore = $currentTabHasMore;
	$: totalCount = $currentTabTotalCount;

	// Debug logging for cache store data changes
	// $: {
	// 	console.log('[DEBUG] +page.svelte: Cache store data updated', {
	// 		currentActiveTab,
	// 		platformCount: currentPlatforms.length,
	// 		platformIds: currentPlatforms.map(p => p.id),
	// 		isLoading,
	// 		hasMore
	// 	});

	// 	// Check for duplicate IDs in cache store data
	// 	if (currentPlatforms.length > 0) {
	// 		const ids = currentPlatforms.map(p => p.id);
	// 		const uniqueIds = [...new Set(ids)];
	// 		if (ids.length !== uniqueIds.length) {
	// 			console.error('[ERROR] +page.svelte: Duplicate platform IDs detected in cache store data:', {
	// 				currentActiveTab,
	// 				allIds: ids,
	// 				duplicates: ids.filter((id, index) => ids.indexOf(id) !== index)
	// 			});
	// 		}
	// 	}
	// }

	let storedStatus = getSubscriptionFromStorage();
	
	onMount(() => {
		// Check subscription status
		if (storedStatus && storedStatus.checked) {
			// If subscription is not active, redirect to subscription page
			if (!storedStatus.is_active) {
				goto('/subscription');
			}
			else {
				// If subscription is active, continue...
				// Initialize the cache store with server data
				tabCacheStore.initialize(data.tabPaginationData || {}, data.access_token, data.username);

				// Start background polling for all tabs
				tabCacheStore.startPolling();

				// Connect WebSocket for real-time updates
				platformWebSocket.connect();
			}
		}
		else {
			// If subscription status is not checked, redirect to login
			goto('/login');
		}
	});

	onDestroy(() => {
		// Stop background polling
		tabCacheStore.stopPolling();
		platformWebSocket.disconnect();
	});

	async function handlePlatformSelect(
		event: CustomEvent<{
			platformId: number;
			customerId: number;
			platformIdentity: CustomerPlatformIdentity;
			isAutoUpdate?: boolean;
		}>
	) {
		const { platformId, customerId, platformIdentity, isAutoUpdate = false } = event.detail;

		// Validate that we have both values
		if (!platformId || !customerId) {
			console.error('Missing platformId or customerId:', { platformId, customerId });
			return;
		}

		// Store the complete platform identity object
		selectedPlatformIdentity = platformIdentity;

		// Extract the latest ticket ID if available (using bracket notation for runtime properties)
		const newLatestTicketId = (platformIdentity as any)['latest_ticket_id'] || null;

		// Log ticket ID changes for debugging
		// if (newLatestTicketId !== latestTicketId) {
		// 	console.log('chat_center/+page.svelte: latestTicketId changed from', latestTicketId, 'to', newLatestTicketId, isAutoUpdate ? '(auto-update)' : '(manual selection)');
		// }

		latestTicketId = newLatestTicketId;

		// Update selected platform ID - this will trigger ConversationView to reload
		selectedPlatformId = platformId;
		selectedCustomerId = customerId;

		// Load customer details if not already loaded or if different customer
		if (!selectedCustomer || selectedCustomer.customer_id !== customerId) {
			await loadCustomerDetails(customerId);
		}
	}

	async function loadCustomerDetails(customerId: number) {
		try {
			loading = true;
			if (!data.access_token) {
				console.error('No access token available');
				return;
			}

			const result = await customerService.getCustomerDetails(customerId, data.access_token);

			if (result.res_status === 200 && result.customer) {
				selectedCustomer = result.customer;
			} else {
				console.error('Error loading customer:', result.error_msg);
			}
			// console.log('loadCustomerDetails: ', selectedCustomer);
		} catch (error) {
			console.error('Error loading customer:', error);
		} finally {
			loading = false;
		}
	}

	// Handle tab change - now instant with caching
	function handleTabChange(event: CustomEvent) {
		const { tab } = event.detail;

		// console.log('[DEBUG] +page.svelte: Tab change requested', {
		// 	previousTab: currentActiveTab,
		// 	newTab: tab,
		// 	currentPlatformCount: currentPlatforms.length,
		// 	totalCount
		// });

		currentActiveTab = tab;

		// Switch tab instantly using cache store
		tabCacheStore.switchTab(tab);

		// console.log('[DEBUG] +page.svelte: Tab change completed', {
		// 	activeTab: currentActiveTab,
		// 	newPlatformCount: currentPlatforms.length,
		// 	totalCount,
		// 	hasMore,
		// 	isLoading
		// });
	}

	// Enhanced load more handler - uses cache store
	async function handleLoadMore(event: CustomEvent) {
		const { tab } = event.detail;

		// console.log('[DEBUG] +page.svelte: Load more requested', {
		// 	requestedTab: tab,
		// 	currentActiveTab,
		// 	currentPlatformCount: currentPlatforms.length,
		// 	totalCount,
		// 	hasMore,
		// 	isLoading
		// });

		// Use cache store to load more items
		await tabCacheStore.loadMore(tab);

		// console.log('[DEBUG] +page.svelte: Load more completed', {
		// 	requestedTab: tab,
		// 	newPlatformCount: currentPlatforms.length,
		// 	totalCount,
		// 	newHasMore: hasMore,
		// 	newIsLoading: isLoading
		// });
	}

	/**
	 * Handle automatic platform updates from PlatformIdentityList polling
	 */
	async function handlePlatformUpdate(event: CustomEvent) {
		const {
			platformId,
			customerId,
			platformIdentity,
			changeType,
			previousTicketId,
			currentTicketId,
			isAutoUpdate
		} = event.detail;

		// console.log('chat_center/+page.svelte: Received automatic platform update:', {
		// 	platformId,
		// 	changeType,
		// 	previousTicketId,
		// 	currentTicketId,
		// 	isAutoUpdate
		// });

		// Use the existing handlePlatformSelect logic for consistency
		await handlePlatformSelect({
			detail: {
				platformId,
				customerId,
				platformIdentity,
				isAutoUpdate: true
			}
		} as CustomEvent);
	}

	/**
	 * Handle general platform data changes notification
	 */
	function handlePlatformDataChanged(event: CustomEvent) {
		const { changedPlatforms, timestamp } = event.detail;

		// console.log('chat_center/+page.svelte: Platform data changed:', {
		// 	changedPlatformsCount: changedPlatforms.length,
		// 	timestamp,
		// 	changedPlatforms
		// });

		// This is just for logging/monitoring purposes
		// The actual updates are handled by handlePlatformUpdate
	}

	// Reactive debugging for data flow verification
	// $: if (latestTicketId) {
	// 	console.log('chat_center/+page.svelte: Reactive - latestTicketId updated:', latestTicketId);
	// }

	// $: if (selectedPlatformId && selectedCustomer) {
	// 	console.log('chat_center/+page.svelte: Reactive - Ready to pass data to ConversationView:', {
	// 		customerId: selectedCustomer.customer_id,
	// 		platformId: selectedPlatformId,
	// 		ticketId: latestTicketId
	// 	});
	// }

	// Compute latest ticket owner ID from platform identity
	$: latestTicketOwnerId = selectedPlatformIdentity ? (selectedPlatformIdentity as any)['latest_ticket_owner_id'] || 0 : 0;

	// Reference to PlatformIdentityList component
	let platformIdentityListRef: any;

	/**
	 * Handle messages marked as read event from ConversationView
	 */
	function handleMessagesMarkedAsRead(event: CustomEvent) {
		const { platformId, messageCount } = event.detail;

		// Update unread count in PlatformIdentityList
		if (platformIdentityListRef && platformIdentityListRef.updateUnreadCount) {
			platformIdentityListRef.updateUnreadCount(platformId, -messageCount);
		}
	}
</script>

<div class="flex h-screen bg-gray-100">
	{#if storedStatus && storedStatus.checked && storedStatus.is_active}
		<!-- Left Panel: All Platform Identities -->
		<div class="flex min-w-[400px] w-1/4 flex-col border-r border-gray-200 bg-white">
			<PlatformIdentityList
				bind:this={platformIdentityListRef}
				platformIdentities={currentPlatforms}
				hasMore={hasMore}
				loading={isLoading}
				{selectedPlatformId}
				activeTab={currentActiveTab}
				access_token={data.access_token}
				currentUserFullName={data.username}
				on:select={handlePlatformSelect}
				on:loadMore={handleLoadMore}
				on:tabChange={handleTabChange}
				on:platformUpdate={handlePlatformUpdate}
				on:platformDataChanged={handlePlatformDataChanged}
			/>
		</div>

		<!-- Middle Panel: Conversation -->
		<div class="flex flex-1 flex-col bg-white">
			{#if selectedPlatformId && selectedCustomer}
				<ConversationView
					customerId={selectedCustomerId}
					platformId={selectedPlatformId}
					ticketId={latestTicketId}
					users={data.allUsers}
					priorities={data.allPriorities}
					statuses={data.allStatuses}
					topics={data.allTopics}
					access_token={data.access_token}
					on:messagesMarkedAsRead={handleMessagesMarkedAsRead}
				/>
			{:else}
				<div class="flex flex-1 items-center justify-center text-gray-500">
					<div class="text-center">
						<svg
							class="mx-auto mb-4 h-12 w-12 text-gray-400"
							fill="none"
							stroke="currentColor"
							viewBox="0 0 24 24"
						>
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
							/>
						</svg>
						<p class="text-lg font-medium">{t('select_conversation')}</p>
						<p class="mt-1 text-sm text-gray-500">{t('choose_platform_identity')}</p>
					</div>
				</div>
			{/if}
		</div>

		<!-- Right Panel: Customer Info -->
		<!-- <div class="w-84 bg-white border-l border-gray-200"> -->
		<div class="flex min-w-[400px] w-1/4 flex-col border-l border-gray-200 bg-white">
			{#if selectedPlatformId && selectedCustomer}
				<CustomerInfoPanel
					customer={selectedCustomer}
					platformId={selectedPlatformId}
					access_token={data.access_token}
					ticketId={latestTicketId}
				/>
			{:else}
				<div class="p-6 text-center text-gray-500">
					<p>{t('select_conversation_view_details')}</p>
				</div>
			{/if}
		</div>
	{/if}
</div>
