#!/usr/bin/env python3
"""
Test script to verify tab-specific pagination is working correctly.
This script tests the backend API endpoints to ensure each tab returns
correct totalCount and pagination metadata.
"""

import requests
import json
from typing import Dict, Any

# Configuration
BASE_URL = "http://localhost:8000"  # Adjust as needed
API_ENDPOINT = f"{BASE_URL}/customer/api/platform-identities/"

def test_tab_pagination(access_token: str, current_user: str):
    """Test pagination for each tab to verify independent totalCount."""
    
    tabs = ['my-assigned', 'my-closed', 'open', 'others-assigned']
    results = {}
    
    print("🔍 Testing Tab-Specific Pagination")
    print("=" * 50)
    
    for tab in tabs:
        print(f"\n📋 Testing tab: {tab}")
        
        # Test first page
        params = {
            'tab': tab,
            'current_user': current_user,
            'page': 1,
            'page_size': 2
        }
        
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }
        
        try:
            response = requests.get(API_ENDPOINT, params=params, headers=headers)
            
            if response.status_code == 200:
                data = response.json()
                
                results[tab] = {
                    'total_count': data.get('count', 0),
                    'page_1_results': len(data.get('results', [])),
                    'has_next': bool(data.get('next')),
                    'has_previous': bool(data.get('previous'))
                }
                
                print(f"  ✅ Total Count: {results[tab]['total_count']}")
                print(f"  ✅ Page 1 Results: {results[tab]['page_1_results']}")
                print(f"  ✅ Has Next: {results[tab]['has_next']}")
                
                # Test second page if available
                if data.get('next'):
                    params['page'] = 2
                    response2 = requests.get(API_ENDPOINT, params=params, headers=headers)
                    
                    if response2.status_code == 200:
                        data2 = response2.json()
                        results[tab]['page_2_results'] = len(data2.get('results', []))
                        print(f"  ✅ Page 2 Results: {results[tab]['page_2_results']}")
                    else:
                        print(f"  ❌ Page 2 request failed: {response2.status_code}")
                else:
                    results[tab]['page_2_results'] = 0
                    print(f"  ✅ No Page 2 (as expected)")
                    
            else:
                print(f"  ❌ Request failed: {response.status_code}")
                print(f"  ❌ Error: {response.text}")
                results[tab] = {'error': f"HTTP {response.status_code}"}
                
        except Exception as e:
            print(f"  ❌ Exception: {str(e)}")
            results[tab] = {'error': str(e)}
    
    # Summary
    print("\n📊 SUMMARY")
    print("=" * 50)
    
    total_across_tabs = 0
    for tab, data in results.items():
        if 'total_count' in data:
            total_across_tabs += data['total_count']
            print(f"{tab:15}: {data['total_count']:3} total items")
        else:
            print(f"{tab:15}: ERROR - {data.get('error', 'Unknown')}")
    
    print(f"{'TOTAL':15}: {total_across_tabs:3} items across all tabs")
    
    # Validation
    print("\n✅ VALIDATION")
    print("=" * 50)
    
    # Check if tabs have independent counts
    unique_counts = set()
    for tab, data in results.items():
        if 'total_count' in data:
            unique_counts.add(data['total_count'])
    
    if len(unique_counts) > 1:
        print("✅ PASS: Tabs have different totalCount values (independent pagination)")
    elif len(unique_counts) == 1:
        print("⚠️  WARNING: All tabs have the same totalCount - may indicate shared pagination")
    else:
        print("❌ FAIL: Could not determine totalCount values")
    
    # Check pagination consistency
    for tab, data in results.items():
        if 'total_count' in data and 'page_1_results' in data:
            total = data['total_count']
            page1 = data['page_1_results']
            page2 = data.get('page_2_results', 0)
            has_next = data.get('has_next', False)
            
            if total <= 2:
                # Should fit in one page
                if page1 == total and not has_next:
                    print(f"✅ {tab}: Correct pagination (all items fit in page 1)")
                else:
                    print(f"❌ {tab}: Incorrect pagination for small dataset")
            else:
                # Should have multiple pages
                if page1 == 2 and has_next:
                    print(f"✅ {tab}: Correct pagination (page 1 full, has next)")
                else:
                    print(f"❌ {tab}: Incorrect pagination for large dataset")
    
    return results

if __name__ == "__main__":
    # You need to provide these values
    ACCESS_TOKEN = input("Enter access token: ").strip()
    CURRENT_USER = input("Enter current username: ").strip()
    
    if not ACCESS_TOKEN or not CURRENT_USER:
        print("❌ Access token and username are required")
        exit(1)
    
    results = test_tab_pagination(ACCESS_TOKEN, CURRENT_USER)
    
    # Save results to file
    with open('tab_pagination_test_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    print(f"\n💾 Results saved to: tab_pagination_test_results.json")
